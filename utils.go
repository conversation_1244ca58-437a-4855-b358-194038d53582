package main

import (
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"
)

func extract(input string, toLower bool) (string, error) {
	fmt.Printf("Extracting from: %s\n", input)
	matches := BracketRegex.FindStringSubmatch(input)

	if len(matches) > 1 {
		if toLower {
			return strings.ToLower(matches[1]), nil
		}
		return matches[1], nil
	}

	return "", fmt.Errorf("no match found")
}

func getDates(cellValue string) (time.Time, time.Time, string, string, error) {
	if cellValue == "" {
		fmt.Println("Cell value is empty")
		return time.Time{}, time.Time{}, "", "", fmt.Errorf("cell value is empty")
	}

	if !strings.Contains(cellValue, "-") {
		fmt.Println("Cell value does not contain '-'")
		return time.Time{}, time.Time{}, "", "", fmt.Errorf("cell value does not contain '-'")
	}

	// Use pre-compiled regex instead of creating new one each time
	if !WeekRegex.MatchString(cellValue) {
		fmt.Println("Cell value does not match expected format 'XX-XX'")
		return time.Time{}, time.Time{}, "", "", fmt.Errorf("cell value does not match expected format 'XX-XX'")
	}

	parts := strings.Split(cellValue, "-")

	year := fmt.Sprintf("%s%s", YearPrefix, parts[0])
	week := strings.TrimSpace(parts[1])

	yearInt, err := strconv.Atoi(year)
	if err != nil {
		fmt.Println("Error parsing year:", err)
		return time.Time{}, time.Time{}, "", "", fmt.Errorf("error parsing year: %w", err)
	}

	weekInt, err := strconv.Atoi(week)
	if err != nil {
		fmt.Println("Error parsing week:", err)
		return time.Time{}, time.Time{}, "", "", fmt.Errorf("error parsing week: %w", err)
	}

	if weekInt < 1 || weekInt > 53 {
		fmt.Printf("Invalid week number: %d. Must be between 1 and 53.\n", weekInt)
		return time.Time{}, time.Time{}, "", "", fmt.Errorf("invalid week number: %d", weekInt)
	}

	jan1 := time.Date(yearInt, time.January, 1, 0, 0, 0, 0, time.UTC)

	weekday := jan1.Weekday()
	if weekday == time.Sunday {
		weekday = 7
	}

	daysToAdd := (weekInt-1)*7 - int(weekday) + 1
	startDate := jan1.AddDate(0, 0, daysToAdd)

	endDate := startDate.Add(7*24*time.Hour - 1*time.Second)

	fmt.Printf("Week %d of %d: %s 00:00 - %s 23:59\n", weekInt, yearInt,
		startDate.Format("2006-01-02"), endDate.Format("2006-01-02"))

	return startDate, endDate, year, week, nil
}

func getEstimatedEffort(cellValue string) (float64, error) {
	cellValue = strings.TrimSpace(cellValue)

	validPercentageRegex := regexp.MustCompile(`^(\d+\.?\d*)\%?$`)
	if !validPercentageRegex.MatchString(cellValue) {
		return 0, fmt.Errorf("invalid estimated effort format: '%s'. Expected numeric value with optional %% symbol", cellValue)
	}

	numericStr := strings.Split(cellValue, "%")[0]

	if numericStr == "" {
		return 0, nil
	}

	fValue, err := strconv.ParseFloat(numericStr, 64)
	if err != nil {
		return 0, fmt.Errorf("error parsing estimated effort: %w", err)
	}

	if strings.Contains(cellValue, "%") {
		fValue = fValue / 100
	}

	maxValue := 1.0
	if !strings.Contains(cellValue, "%") {
		maxValue = 100.0
		fValue = fValue / 100 // Convert to decimal for consistency
	}

	if fValue < 0 || fValue > maxValue {
		return 0, fmt.Errorf("estimated effort must be between 0 and %.0f%%, got %.2f", maxValue*100, fValue*100)
	}

	fmt.Printf("Estimated effort: %.2f\n", fValue)
	return fValue, nil
}
