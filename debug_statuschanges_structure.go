package main

import (
	timesheetMysql "excelize-test/storage/repository/mysql"
	"fmt"
	"github.com/joho/godotenv"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"log"
	"os"
)

func main() {
	// Инициализация БД
	err := initDB()
	if err != nil {
		log.Fatalf("Error initializing database: %v", err)
	}

	// 1. Проверим структуру таблицы
	log.Println("=== TABLE STRUCTURE ===")
	var tableInfo []map[string]interface{}
	err = timesheetMysql.Db.Raw("DESCRIBE statuschanges_table").Scan(&tableInfo).Error
	if err != nil {
		log.Printf("Error describing table: %v", err)
	} else {
		for _, row := range tableInfo {
			log.Printf("Column: %+v", row)
		}
	}

	// 2. Проверим количество записей
	log.Println("\n=== RECORD COUNT ===")
	var count int64
	err = timesheetMysql.Db.Table("statuschanges_table").Count(&count).Error
	if err != nil {
		log.Printf("Error counting records: %v", err)
	} else {
		log.Printf("Total records in statuschanges_table: %d", count)
	}

	// 3. Проверим первые 5 записей
	log.Println("\n=== SAMPLE DATA ===")
	var sampleData []map[string]interface{}
	err = timesheetMysql.Db.Raw("SELECT * FROM statuschanges_table LIMIT 5").Scan(&sampleData).Error
	if err != nil {
		log.Printf("Error getting sample data: %v", err)
	} else {
		for i, row := range sampleData {
			log.Printf("Record %d: %+v", i+1, row)
		}
	}

	// 4. Проверим записи для конкретного ключа
	log.Println("\n=== SPECIFIC KEY TEST ===")
	testKeys := []string{"NUIT-2325", "NUIT-2394", "NUIT-2395"}
	for _, key := range testKeys {
		var keyData []map[string]interface{}
		err = timesheetMysql.Db.Raw("SELECT * FROM statuschanges_table WHERE `key` = ? LIMIT 3", key).Scan(&keyData).Error
		if err != nil {
			log.Printf("Error getting data for key %s: %v", key, err)
		} else {
			log.Printf("Records for key %s: %d", key, len(keyData))
			for _, row := range keyData {
				log.Printf("  %+v", row)
			}
		}
	}

	// 5. Проверим уникальные ключи
	log.Println("\n=== UNIQUE KEYS ===")
	var uniqueKeys []string
	err = timesheetMysql.Db.Raw("SELECT DISTINCT `key` FROM statuschanges_table LIMIT 10").Scan(&uniqueKeys).Error
	if err != nil {
		log.Printf("Error getting unique keys: %v", err)
	} else {
		log.Printf("Sample unique keys (%d): %v", len(uniqueKeys), uniqueKeys)
	}

	// 6. Проверим диапазон дат
	log.Println("\n=== DATE RANGE ===")
	var dateInfo []map[string]interface{}
	err = timesheetMysql.Db.Raw("SELECT MIN(date) as min_date, MAX(date) as max_date, COUNT(*) as total FROM statuschanges_table").Scan(&dateInfo).Error
	if err != nil {
		log.Printf("Error getting date range: %v", err)
	} else {
		for _, row := range dateInfo {
			log.Printf("Date range: %+v", row)
		}
	}
}

func initDB() error {
	err := godotenv.Load()
	if err != nil {
		log.Println("No .env file found, using environment variables")
	}

	dbUser := os.Getenv("DB_USER")
	dbPassword := os.Getenv("DB_PASSWORD")
	dbHost := os.Getenv("DB_HOST")
	dbPort := os.Getenv("DB_PORT")
	dbName := os.Getenv("DB_NAME")

	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		dbUser, dbPassword, dbHost, dbPort, dbName)

	gormDB, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		return err
	}
	
	timesheetMysql.Db = gormDB
	return nil
}
