package main

import (
	timesheetMysql "excelize-test/storage/repository/mysql"
	"fmt"
	"github.com/joho/godotenv"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"log"
	"os"
)

func main() {
	// Инициализация БД
	err := initDB()
	if err != nil {
		log.Fatalf("Error initializing database: %v", err)
	}

	// Отладка: проверим структуру таблицы и данные
	scRepo := timesheetMysql.NewStatusChangeRepository()
	
	log.Println("=== DEBUGGING TABLE STRUCTURE ===")
	err = scRepo.GetTableInfo()
	if err != nil {
		log.Printf("Warning: Could not get table info: %v", err)
	}
	
	log.Println("=== DEBUGGING SAMPLE DATA ===")
	err = scRepo.GetSampleData()
	if err != nil {
		log.Printf("Warning: Could not get sample data: %v", err)
	}

	// Тестируем конкретный запрос
	log.Println("=== TESTING SPECIFIC QUERY ===")
	changes, err := scRepo.GetByKey("NUIT-2325")
	if err != nil {
		log.Printf("Error getting changes for NUIT-2325: %v", err)
	} else {
		log.Printf("Found %d changes for NUIT-2325", len(changes))
		for _, change := range changes {
			log.Printf("  Change: %+v", change)
		}
	}
}

func initDB() error {
	err := godotenv.Load()
	if err != nil {
		log.Println("No .env file found, using environment variables")
	}

	dbUser := os.Getenv("DB_USER")
	dbPassword := os.Getenv("DB_PASSWORD")
	dbHost := os.Getenv("DB_HOST")
	dbPort := os.Getenv("DB_PORT")
	dbName := os.Getenv("DB_NAME")

	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		dbUser, dbPassword, dbHost, dbPort, dbName)

	gormDB, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		return err
	}
	
	timesheetMysql.Db = gormDB
	return nil
}
