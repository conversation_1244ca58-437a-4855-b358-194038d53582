package main

import (
	"excelize-test/storage/repository/mysql"
	"excelize-test/storage/usecase"
	"fmt"
	"mime/multipart"
	"net/http"
	"path/filepath"
	"strings"
)

func handleUpload(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	if r.Method != "POST" {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	contentType := r.Header.Get("Content-Type")
	if !strings.HasPrefix(contentType, "multipart/form-data") {
		http.Error(w, "Content-Type must be multipart/form-data", http.StatusBadRequest)
		return
	}

	err := r.ParseMultipartForm(10 << 20)
	if err != nil {
		fmt.Printf("ParseMultipartForm error: %v\n", err)
		http.Error(w, fmt.Sprintf("Failed to parse multipart form: %v", err), http.StatusBadRequest)
		return
	}

	file, fileHeader, err := r.FormFile("file")
	if err != nil {
		fmt.Printf("FormFile error: %v\n", err)
		http.Error(w, fmt.Sprintf("Failed to get file from form: %v", err), http.StatusBadRequest)
		return
	}
	defer func() {
		if closeErr := file.Close(); closeErr != nil {
			fmt.Printf("Error closing uploaded file: %v\n", closeErr)
		}
	}()

	// Validate file
	if err := validateFile(fileHeader); err != nil {
		fmt.Printf("File validation error: %v\n", err)
		http.Error(w, fmt.Sprintf("Invalid file: %v", err), http.StatusBadRequest)
		return
	}

	records, err := processExcelFile(file)
	if err != nil {
		fmt.Printf("Excel processing error: %v\n", err)
		http.Error(w, fmt.Sprintf("Failed to process Excel file: %v", err), http.StatusInternalServerError)
		return
	}

	repository := mysql.NewTimesheetRepository()
	uc := usecase.NewTimesheetUsecase(repository)

	err = uc.Update(records)
	if err != nil {
		fmt.Printf("Database update error: %v\n", err)
		http.Error(w, fmt.Sprintf("Failed to save records to database: %v", err), http.StatusInternalServerError)
		return
	}

	// Return success response with record count
	w.WriteHeader(http.StatusOK)
	fmt.Fprintf(w, `{"success": true, "message": "File processed successfully", "records": %d}`, len(records))
}

func validateFile(fileHeader *multipart.FileHeader) error {
	// Check file size (max 10MB)
	maxSize := int64(10 << 20)
	if fileHeader.Size > maxSize {
		return fmt.Errorf("file too large: %d bytes (max %d bytes)", fileHeader.Size, maxSize)
	}

	// Check file extension
	ext := strings.ToLower(filepath.Ext(fileHeader.Filename))
	allowedExts := []string{".xlsx", ".xlsm", ".xls"}

	valid := false
	for _, allowedExt := range allowedExts {
		if ext == allowedExt {
			valid = true
			break
		}
	}

	if !valid {
		return fmt.Errorf("invalid file type: %s (allowed: %v)", ext, allowedExts)
	}

	return nil
}
