package models

// TimesheetRecord represents a single timesheet entry
type TimesheetRecord struct {
	PK             string
	Initials       string
	Epic           string // [XXXX-XXXX]
	Year           string
	Week           string
	Startdate      string
	Enddate        string
	EstimateEffort float64 // 0.6
}

type TimesheetRecords []TimesheetRecord

// BurndownRecord represents a burndown chart data point for an epic
type BurndownRecord struct {
	PK     string  `json:"pk"`
	EpicID string  `json:"epic_id"`
	Year   int     `json:"year"`
	Week   int     `json:"week"`
	Actual float64 `json:"actual"`
	Ideal  float64 `json:"ideal"`
}

type BurndownRecords []BurndownRecord
