package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"github.com/xuri/excelize/v2"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"io"
	"log"
	"net/http"
	"strings"
	"time"
)

// TimeEntry represents a time tracking record
type TimeEntry struct {
	ID          uint      `gorm:"primaryKey" json:"id"`
	Date        time.Time `gorm:"type:date;not null" json:"date"`
	Employee    string    `gorm:"size:255;not null" json:"employee"`
	Project     string    `gorm:"size:255;not null" json:"project"`
	Hours       float64   `gorm:"type:decimal(5,2);not null" json:"hours"`
	Description string    `gorm:"type:text" json:"description"`
	Total       float64   `gorm:"type:decimal(10,2);not null" json:"total"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// TableName sets the table name for GORM
func (TimeEntry) TableName() string {
	return "time_entries"
}

// GraphClient handles Microsoft Graph API operations
type GraphClient struct {
	AccessToken string
	BaseURL     string
}

// TokenResponse represents Azure AD token response
type TokenResponse struct {
	AccessToken string `json:"access_token"`
	TokenType   string `json:"token_type"`
	ExpiresIn   int    `json:"expires_in"`
}

// Config holds application configuration
type Config struct {
	TenantID     string
	ClientID     string
	ClientSecret string
	SiteID       string
	DriveID      string
	ItemID       string
	DBConnection string
}

// ETLProcessor handles the entire ETL pipeline
type ETLProcessor struct {
	Config *Config
	DB     *gorm.DB
	Client *GraphClient
}

func start() {
	config := Config{
		TenantID:     "your-tenant-id",
		ClientID:     "your-client-id",
		ClientSecret: "your-client-secret",
		SiteID:       "your-site-id",
		DriveID:      "your-drive-id",
		ItemID:       "your-excel-file-id",
		DBConnection: "user:password@tcp(localhost:3306)/timesheet_db?charset=utf8mb4&parseTime=True&loc=Local",
	}

	processor, err := NewETLProcessor(&config)
	if err != nil {
		log.Fatal("Failed to initialize ETL processor:", err)
	}
	defer processor.Close()

	// Run the ETL process
	if err := processor.Run(); err != nil {
		log.Fatal("ETL process failed:", err)
	}

	log.Println("ETL process completed successfully")
}

// NewETLProcessor creates a new ETL processor instance
func NewETLProcessor(config *Config) (*ETLProcessor, error) {
	// Initialize database connection
	db, err := initDatabase(config.DBConnection)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize database: %w", err)
	}

	// Get access token
	accessToken, err := getAccessToken(config.TenantID, config.ClientID, config.ClientSecret)
	if err != nil {
		return nil, fmt.Errorf("failed to get access token: %w", err)
	}

	// Create Graph API client
	client := &GraphClient{
		AccessToken: accessToken,
		BaseURL:     "https://graph.microsoft.com/v1.0",
	}

	return &ETLProcessor{
		Config: config,
		DB:     db,
		Client: client,
	}, nil
}

// Close closes database connection
func (e *ETLProcessor) Close() {
	if sqlDB, err := e.DB.DB(); err == nil {
		sqlDB.Close()
	}
}

// Run executes the complete ETL pipeline
func (e *ETLProcessor) Run() error {
	log.Println("Starting ETL process...")

	// Extract data from SharePoint Excel
	timeEntries, err := e.Client.extractDataFromExcel(e.Config.SiteID, e.Config.DriveID, e.Config.ItemID)
	if err != nil {
		return fmt.Errorf("failed to extract data: %w", err)
	}
	log.Printf("Extracted %d records from Excel", len(timeEntries))

	// Transform (filter) data
	filteredEntries := e.filterData(timeEntries)
	log.Printf("After filtering: %d records", len(filteredEntries))

	// Load data to database
	if err := e.saveToDatabase(filteredEntries); err != nil {
		return fmt.Errorf("failed to save to database: %w", err)
	}
	log.Printf("Successfully saved %d records to database", len(filteredEntries))

	return nil
}

// initDatabase initializes database connection with GORM
func initDatabase(dsn string) (*gorm.DB, error) {
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		return nil, err
	}

	// Auto-migrate the schema
	if err := db.AutoMigrate(&TimeEntry{}); err != nil {
		return nil, fmt.Errorf("failed to migrate database: %w", err)
	}

	// Add unique index for preventing duplicates
	if err := db.Exec("CREATE UNIQUE INDEX IF NOT EXISTS idx_unique_entry ON time_entries (date, employee, project)").Error; err != nil {
		// Index might already exist, log but don't fail
		log.Printf("Warning: Could not create unique index: %v", err)
	}

	return db, nil
}

// getAccessToken obtains Azure AD access token
func getAccessToken(tenantID, clientID, clientSecret string) (string, error) {
	url := fmt.Sprintf("https://login.microsoftonline.com/%s/oauth2/v2.0/token", tenantID)

	data := fmt.Sprintf("grant_type=client_credentials&client_id=%s&client_secret=%s&scope=https://graph.microsoft.com/.default",
		clientID, clientSecret)

	req, err := http.NewRequest("POST", url, strings.NewReader(data))
	if err != nil {
		return "", err
	}
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("authentication failed with status: %d", resp.StatusCode)
	}

	var tokenResp TokenResponse
	if err := json.NewDecoder(resp.Body).Decode(&tokenResp); err != nil {
		return "", err
	}

	return tokenResp.AccessToken, nil
}

// extractDataFromExcel downloads and processes Excel file from SharePoint
func (g *GraphClient) extractDataFromExcel(siteID, driveID, itemID string) ([]TimeEntry, error) {
	// Download file from SharePoint
	url := fmt.Sprintf("%s/sites/%s/drives/%s/items/%s/content", g.BaseURL, siteID, driveID, itemID)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, err
	}
	req.Header.Set("Authorization", "Bearer "+g.AccessToken)

	client := &http.Client{Timeout: 60 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("failed to download file, HTTP status: %d", resp.StatusCode)
	}

	// Read file content
	content, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	// Parse Excel data
	return g.parseExcelData(content)
}

// parseExcelData extracts data from Excel file content
func (g *GraphClient) parseExcelData(content []byte) ([]TimeEntry, error) {
	f, err := excelize.OpenReader(bytes.NewReader(content))
	if err != nil {
		return nil, err
	}
	defer f.Close()

	// Get all rows from the timesheet
	rows, err := f.GetRows("Timesheet") // Change to your sheet name
	if err != nil {
		return nil, err
	}

	var entries []TimeEntry

	// Skip header row (first row)
	for i, row := range rows {
		if i == 0 {
			continue // Skip header
		}

		entry, err := g.parseRowToTimeEntry(row, i+1)
		if err != nil {
			log.Printf("Error parsing row %d: %v", i+1, err)
			continue
		}

		entries = append(entries, entry)
	}

	return entries, nil
}

// parseRowToTimeEntry converts Excel row to TimeEntry struct
func (g *GraphClient) parseRowToTimeEntry(row []string, rowNum int) (TimeEntry, error) {
	var entry TimeEntry

	// Parse date (column A)
	if dateStr := strings.TrimSpace(row[0]); dateStr != "" {
		// Try different date formats
		formats := []string{"02.01.2006", "2006-01-02", "01/02/2006", "2006/01/02"}
		var parsedDate time.Time
		var err error

		for _, format := range formats {
			if parsedDate, err = time.Parse(format, dateStr); err == nil {
				break
			}
		}

		if err != nil {
			return entry, fmt.Errorf("invalid date format in row %d: %s", rowNum, dateStr)
		}
		entry.Date = parsedDate
	}

	// Employee (column B)
	entry.Employee = strings.TrimSpace(row[1])

	// Project (column C)
	entry.Project = strings.TrimSpace(row[2])

	// Hours (column D)
	if hoursStr := strings.TrimSpace(row[3]); hoursStr != "" {
		if _, err := fmt.Sscanf(hoursStr, "%f", &entry.Hours); err != nil {
			return entry, fmt.Errorf("invalid hours format in row %d: %s", rowNum, hoursStr)
		}
	}

	// Description (column E)
	entry.Description = strings.TrimSpace(row[4])

	return entry, nil
}

// filterData applies business logic filters to the data
func (e *ETLProcessor) filterData(entries []TimeEntry) []TimeEntry {
	var filtered []TimeEntry
	now := time.Now()
	cutoffDate := now.AddDate(0, 0, -30) // Last 30 days

	for _, entry := range entries {
		if e.shouldIncludeEntry(entry, cutoffDate) {
			filtered = append(filtered, entry)
		}
	}

	return filtered
}

// shouldIncludeEntry determines if an entry should be included based on business rules
func (e *ETLProcessor) shouldIncludeEntry(entry TimeEntry, cutoffDate time.Time) bool {
	// Filter 1: Only records from last 30 days
	if entry.Date.Before(cutoffDate) {
		return false
	}

	// Filter 2: Exclude records with 0 or negative hours
	if entry.Hours <= 0 {
		return false
	}

	// Filter 3: Exclude records with unrealistic hours (possible data errors)
	if entry.Hours > 24 {
		return false
	}

	// Filter 4: Only records with employee name
	if strings.TrimSpace(entry.Employee) == "" {
		return false
	}

	// Filter 5: Only records with project name
	if strings.TrimSpace(entry.Project) == "" {
		return false
	}

	return true
}

// saveToDatabase saves filtered entries to MySQL using GORM
func (e *ETLProcessor) saveToDatabase(entries []TimeEntry) error {
	if len(entries) == 0 {
		log.Println("No entries to save")
		return nil
	}

	// Use transaction for batch operation
	return e.DB.Transaction(func(tx *gorm.DB) error {
		for _, entry := range entries {
			// Use GORM's save method which handles insert/update
			var existingEntry TimeEntry
			result := tx.Where("date = ? AND employee = ? AND project = ?",
				entry.Date, entry.Employee, entry.Project).First(&existingEntry)

			if result.Error == gorm.ErrRecordNotFound {
				// Create new record
				if err := tx.Create(&entry).Error; err != nil {
					return fmt.Errorf("failed to create entry: %w", err)
				}
			} else if result.Error == nil {
				// Update existing record
				entry.ID = existingEntry.ID
				entry.CreatedAt = existingEntry.CreatedAt
				if err := tx.Save(&entry).Error; err != nil {
					return fmt.Errorf("failed to update entry: %w", err)
				}
			} else {
				return fmt.Errorf("database error: %w", result.Error)
			}
		}
		return nil
	})
}

// GetFileInfo retrieves file metadata from SharePoint (utility function)
func (g *GraphClient) GetFileInfo(siteID, driveID, itemID string) (map[string]interface{}, error) {
	url := fmt.Sprintf("%s/sites/%s/drives/%s/items/%s", g.BaseURL, siteID, driveID, itemID)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, err
	}
	req.Header.Set("Authorization", "Bearer "+g.AccessToken)

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	var fileInfo map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&fileInfo); err != nil {
		return nil, err
	}

	return fileInfo, nil
}

// GetEntriesByDateRange retrieves time entries within a date range
func (e *ETLProcessor) GetEntriesByDateRange(startDate, endDate time.Time) ([]TimeEntry, error) {
	var entries []TimeEntry
	result := e.DB.Where("date BETWEEN ? AND ?", startDate, endDate).Find(&entries)
	return entries, result.Error
}

// GetEntriesByEmployee retrieves all time entries for a specific employee
func (e *ETLProcessor) GetEntriesByEmployee(employee string) ([]TimeEntry, error) {
	var entries []TimeEntry
	result := e.DB.Where("employee = ?", employee).Find(&entries)
	return entries, result.Error
}

// GetEntriesByProject retrieves all time entries for a specific project
func (e *ETLProcessor) GetEntriesByProject(project string) ([]TimeEntry, error) {
	var entries []TimeEntry
	result := e.DB.Where("project = ?", project).Find(&entries)
	return entries, result.Error
}

// DeleteEntriesOlderThan removes entries older than specified date
func (e *ETLProcessor) DeleteEntriesOlderThan(cutoffDate time.Time) error {
	result := e.DB.Where("date < ?", cutoffDate).Delete(&TimeEntry{})
	log.Printf("Deleted %d old entries", result.RowsAffected)
	return result.Error
}
