package main

import "regexp"

// Constants for configuration
const (
	ExcelFileName    = "nuIT-Ressource Planning.xlsx"
	WorksheetName    = "Tabelle1"
	MaxSearchRows    = 100
	MaxSearchColumns = 400
	NameColumn       = "B"
	EpicColumn       = "D"
	HeaderRow        = 4
	DateFormat       = "2006-01-02"
	YearPrefix       = "20"
)

// Pre-compiled regular expressions to avoid duplicating compilation
var (
	BracketRegex = regexp.MustCompile(`\[([^]]+)]`)
	WeekRegex    = regexp.MustCompile(`^\d{2}-\d{2}$`)
)
