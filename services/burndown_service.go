package services

import (
	"excelize-test/models"
	"excelize-test/storage/repository/mysql"
	"fmt"
	"log"
	"strings"
)

type BurndownService struct {
	burndownRepo *mysql.BurndownRepository
}

func NewBurndownService() *BurndownService {
	return &BurndownService{
		burndownRepo: mysql.NewBurndownRepository(),
	}
}

func (s *BurndownService) CalculateBurndownForEpic(epicID string, issues []mysql.Issue, startYear, startWeek, endYear, endWeek int) (models.BurndownRecords, error) {
	epicIssues := s.filterIssuesForEpic(issues, epicID)
	totalEstimate := s.calculateTotalEstimate(epicIssues)

	var burndownRecords models.BurndownRecords

	for year := startYear; year <= endYear; year++ {
		weekStart := 1
		weekEnd := 52

		if year == startYear {
			weekStart = startWeek
		}
		if year == endYear {
			weekEnd = endWeek
		}

		for week := weekStart; week <= weekEnd; week++ {
			actual := s.calculateActualRemaining(epicIssues)
			workCompleted := s.calculateWorkCompletedByWeek(epicID, year, week)
			ideal := totalEstimate - workCompleted
			if ideal < 0 {
				ideal = 0
			}

			record := models.BurndownRecord{
				PK:     fmt.Sprintf("%s_%d_%d", epicID, year, week),
				EpicID: epicID,
				Year:   year,
				Week:   week,
				Actual: actual,
				Ideal:  ideal,
			}

			burndownRecords = append(burndownRecords, record)
		}
	}

	return burndownRecords, nil
}

func (s *BurndownService) filterIssuesForEpic(issues []mysql.Issue, epicID string) []mysql.Issue {
	var epicIssues []mysql.Issue

	for _, issue := range issues {
		if issue.Key == epicID || issue.ParentKey == epicID {
			epicIssues = append(epicIssues, issue)
		}
	}

	return epicIssues
}

func (s *BurndownService) calculateTotalEstimate(issues []mysql.Issue) float64 {
	total := 0.0
	for _, issue := range issues {
		total += float64(issue.TimeOriginalEstimate) / 3600.0
	}
	return total
}

func (s *BurndownService) calculateActualRemaining(issues []mysql.Issue) float64 {
	remaining := 0.0

	for _, issue := range issues {
		if !s.isTaskCompleted(issue.StatusName) {
			remaining += float64(issue.TimeOriginalEstimate) / 3600.0
		}
	}

	return remaining
}

func (s *BurndownService) isTaskCompleted(statusName string) bool {
	completedStatuses := []string{"Done Develop"}
	statusLower := strings.ToLower(statusName)

	for _, status := range completedStatuses {
		if strings.ToLower(status) == statusLower {
			return true
		}
	}

	return false
}

func (s *BurndownService) calculateWorkCompletedByWeek(epicID string, targetYear, targetWeek int) float64 {
	var timesheetRecords []mysql.TimesheetRecord

	query := mysql.Db.Where("epic = ?", epicID)
	query = query.Where(
		"(CAST(year AS UNSIGNED) < ?) OR (CAST(year AS UNSIGNED) = ? AND CAST(week AS UNSIGNED) <= ?)",
		targetYear, targetYear, targetWeek,
	)

	err := query.Find(&timesheetRecords).Error
	if err != nil {
		log.Printf("Error querying timesheet records for epic %s: %v", epicID, err)
		return 0.0
	}

	totalWork := 0.0
	for _, record := range timesheetRecords {
		totalWork += record.EstimateEffort
	}

	return totalWork
}

func (s *BurndownService) SaveBurndownData(epicID string, records models.BurndownRecords) error {
	return s.burndownRepo.Update(epicID, records)
}

func (s *BurndownService) GetBurndownData(epicID string) (models.BurndownRecords, error) {
	return s.burndownRepo.GetByEpic(epicID)
}
