package main

import (
	"excelize-test/storage/repository/mysql"
	"excelize-test/storage/usecase"
	"fmt"
	"github.com/go-chi/chi/v5"
	"github.com/go-chi/cors"
	"log"
	"net/http"
)

func main() {

	err := DbSetup()
	if err != nil {
		log.Fatalf("Error setting up database: %v", err)
	}

	r := chi.NewRouter()
	r.Use(cors.<PERSON>ler(cors.Options{
		AllowedOrigins:   []string{"*"},
		AllowedMethods:   []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowedHeaders:   []string{"Accept", "Authorization", "Content-Type"},
		ExposedHeaders:   []string{"Link"},
		AllowCredentials: true,
		MaxAge:           300,
	}))

	r.Post("/upload", handleUpload)

	fmt.Println("Server starting on :8080")

	burndownService := usecase.NewBurndownUsecase()

	issueRepo := mysql.NewIssueRepository()
	issues, err := issueRepo.GetByEpicID("NUIT-2325")
	if err != nil {
		log.Fatalf("Error getting issues: %v", err)
	}

	epicID := "NUIT-2325"
	fmt.Printf("Calculating burndown for epic %s\n", epicID)
	records, err := burndownService.CalculateBurndownForEpic(
		epicID, issues, 2025, 29, 2025, 52)
	if err != nil {
		log.Fatalf("Error calculating burndown: %v", err)
	}

	err = burndownService.SaveBurndownData(epicID, records)
	if err != nil {
		log.Fatalf("Error saving burndown data: %v", err)
	}

	fmt.Printf("Successfully calculated and saved %d burndown records for epic %s\n",
		len(records), epicID)

	retrievedRecords, err := burndownService.GetBurndownData(epicID)
	if err != nil {
		log.Fatalf("Error retrieving burndown data: %v", err)
	}

	fmt.Printf("Retrieved %d records from database\n", len(retrievedRecords))

	for _, record := range retrievedRecords {

		fmt.Printf("Week %d-%d: Actual=%.2f weeks, Ideal=%.2f weeks\n",
			record.Year, record.Week, record.Actual/24/7*4.2, record.Ideal/24/7*4.2)
	}

	err = http.ListenAndServe(":8080", r)
	if err != nil {
		fmt.Printf("Server error: %v\n", err)
	}
}
