package main

import (
	"fmt"
	"github.com/go-chi/chi/v5"
	"github.com/go-chi/cors"
	"log"
	"net/http"
)

func main() {

	err := DbSetup()
	if err != nil {
		log.Fatalf("Error setting up database: %v", err)
	}

	r := chi.NewRouter()
	r.Use(cors.Handler(cors.Options{
		AllowedOrigins:   []string{"*"},
		AllowedMethods:   []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowedHeaders:   []string{"Accept", "Authorization", "Content-Type"},
		ExposedHeaders:   []string{"Link"},
		AllowCredentials: true,
		MaxAge:           300,
	}))

	r.Post("/upload", handleUpload)

	fmt.Println("Server starting on :8080")
	err = http.ListenAndServe(":8080", r)
	if err != nil {
		fmt.Printf("Server error: %v\n", err)
	}
}
