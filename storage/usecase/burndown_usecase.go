package usecase

import (
	"excelize-test/models"
	"excelize-test/storage/repository/mysql"
	"fmt"
	"log"
	"strings"
	"time"
)

type BurndownUsecase struct {
	burndownRepo *mysql.BurndownRepository
	issueRepo    *mysql.IssueRepository
}

func NewBurndownUsecase() *BurndownUsecase {
	return &BurndownUsecase{
		burndownRepo: mysql.NewBurndownRepository(),
		issueRepo:    mysql.NewIssueRepository(),
	}
}

func (s *BurndownUsecase) CalculateBurndownForEpic(epicID string, issues []mysql.Issue, startYear, startWeek, endYear, endWeek int) (models.BurndownRecords, error) {
	epicIssues := s.filterIssuesForEpic(issues, epicID)
	totalEstimate := s.calculateTotalEstimate(epicIssues)

	var burndownRecords models.BurndownRecords

	for year := startYear; year <= endYear; year++ {
		weekStart := 1
		weekEnd := 52

		if year == startYear {
			weekStart = startWeek
		}
		if year == endYear {
			weekEnd = endWeek
		}

		for week := weekStart; week <= weekEnd; week++ {
			actual := s.calculateActualRemainingForWeek(epicIssues, year, week)
			workCompleted := s.calculateWorkCompletedByWeek(epicID, year, week)
			ideal := totalEstimate - workCompleted
			if ideal < 0 {
				ideal = 0
			}

			record := models.BurndownRecord{
				PK:     fmt.Sprintf("%s_%d_%d", epicID, year, week),
				EpicID: epicID,
				Year:   year,
				Week:   week,
				Actual: actual,
				Ideal:  ideal,
			}

			burndownRecords = append(burndownRecords, record)
		}
	}

	return burndownRecords, nil
}

func (s *BurndownUsecase) filterIssuesForEpic(issues []mysql.Issue, epicID string) []mysql.Issue {
	var epicIssues []mysql.Issue

	for _, issue := range issues {
		if issue.Key == epicID || issue.ParentKey == epicID {
			epicIssues = append(epicIssues, issue)
		}
	}

	return epicIssues
}

func (s *BurndownUsecase) calculateTotalEstimate(issues []mysql.Issue) float64 {
	total := 0.0
	for _, issue := range issues {
		total += float64(issue.TimeOriginalEstimate) / 3600.0
	}
	return total
}

func (s *BurndownUsecase) calculateActualRemaining(issues []mysql.Issue) float64 {
	remaining := 0.0

	for _, issue := range issues {
		if !s.isTaskCompleted(issue.StatusName) {
			remaining += float64(issue.TimeOriginalEstimate) / 3600.0
		}
	}

	return remaining
}

func (s *BurndownUsecase) calculateActualRemainingForWeek(issues []mysql.Issue, targetYear, targetWeek int) float64 {
	remaining := 0.0
	scRepo := mysql.NewStatusChangeRepository()

	// Конвертируем год и неделю в timestamp конца недели
	weekEndTimestamp := s.getWeekEndTimestamp(targetYear, targetWeek)
	log.Printf("DEBUG: Calculating actual for week %d-%d (timestamp: %d)", targetYear, targetWeek, weekEndTimestamp)

	completedCount := 0
	totalCount := len(issues)

	for _, issue := range issues {
		// Получаем статус задачи на конец указанной недели
		statusAtWeek := s.getIssueStatusAtDate(issue, weekEndTimestamp, scRepo)

		// Если задача не была завершена на тот момент, добавляем её estimate
		if !s.isTaskCompleted(statusAtWeek) {
			hours := float64(issue.TimeOriginalEstimate) / 3600.0
			remaining += hours
			log.Printf("DEBUG: Issue %s - Status: %s (NOT completed) - Adding %.2f hours", issue.Key, statusAtWeek, hours)
		} else {
			completedCount++
			log.Printf("DEBUG: Issue %s - Status: %s (COMPLETED) - Skipping", issue.Key, statusAtWeek)
		}
	}

	log.Printf("DEBUG: Week %d-%d summary: %d/%d issues completed, %.2f hours remaining",
		targetYear, targetWeek, completedCount, totalCount, remaining)
	return remaining
}

func (s *BurndownUsecase) isTaskCompleted(statusName string) bool {
	completedStatuses := []string{"Done Develop*"}
	statusLower := strings.ToLower(statusName)

	for _, status := range completedStatuses {
		if strings.ToLower(status) == statusLower {
			return true
		}
	}

	return false
}

// getIssueStatusAtDate возвращает статус задачи на определенную дату
func (s *BurndownUsecase) getIssueStatusAtDate(issue mysql.Issue, targetTimestamp uint64, scRepo *mysql.StatusChangeRepository) string {
	// Получаем все изменения статуса до указанной даты
	changes, err := scRepo.GetByKeyBeforeDate(issue.Key, targetTimestamp)
	if err != nil {
		log.Printf("Error getting status changes for issue %s: %v", issue.Key, err)
		// Если нет данных об изменениях, используем текущий статус
		return issue.StatusName
	}

	// Если нет изменений до указанной даты, используем текущий статус из issues_table
	// Это означает, что либо задача не меняла статус, либо данные об изменениях отсутствуют
	if len(changes) == 0 {
		log.Printf("DEBUG: No status changes found for %s, using current status: %s", issue.Key, issue.StatusName)
		return issue.StatusName
	}

	// Возвращаем последний статус до указанной даты
	lastChange := changes[len(changes)-1]
	log.Printf("DEBUG: Using historical status for %s: %s (from %d changes)", issue.Key, lastChange.ToStatusName, len(changes))
	return lastChange.ToStatusName
}

// getWeekEndTimestamp конвертирует год и неделю в timestamp конца недели
func (s *BurndownUsecase) getWeekEndTimestamp(year, week int) uint64 {
	// Находим первый понедельник года
	jan1 := time.Date(year, 1, 1, 0, 0, 0, 0, time.UTC)

	// Находим первый понедельник года
	daysToMonday := (8 - int(jan1.Weekday())) % 7
	if jan1.Weekday() == time.Sunday {
		daysToMonday = 1
	}
	firstMonday := jan1.AddDate(0, 0, daysToMonday)

	// Добавляем недели и переходим к воскресенью (концу недели)
	targetWeekStart := firstMonday.AddDate(0, 0, (week-1)*7)
	weekEnd := targetWeekStart.AddDate(0, 0, 6).Add(23*time.Hour + 59*time.Minute + 59*time.Second)

	return uint64(weekEnd.Unix())
}

func (s *BurndownUsecase) calculateWorkCompletedByWeek(epicID string, targetYear, targetWeek int) float64 {
	var timesheetRecords []mysql.TimesheetRecord

	query := mysql.Db.Where("epic = ?", epicID)
	query = query.Where(
		"(CAST(year AS UNSIGNED) < ?) OR (CAST(year AS UNSIGNED) = ? AND CAST(week AS UNSIGNED) <= ?)",
		targetYear, targetYear, targetWeek,
	)

	err := query.Find(&timesheetRecords).Error
	if err != nil {
		log.Printf("Error querying timesheet records for epic %s: %v", epicID, err)
		return 0.0
	}

	totalWork := 0.0
	const HOURS = 40
	for _, record := range timesheetRecords {
		totalWork += record.EstimateEffort * HOURS
	}

	return totalWork
}

func (s *BurndownUsecase) SaveBurndownData(epicID string, records models.BurndownRecords) error {
	return s.burndownRepo.Update(epicID, records)
}

func (s *BurndownUsecase) GetBurndownData(epicID string) (models.BurndownRecords, error) {
	return s.burndownRepo.GetByEpic(epicID)
}

func (s *BurndownUsecase) CalculateAndSaveBurndownForEpic(epicID string, startYear, startWeek, endYear, endWeek int) error {

	issues, err := s.issueRepo.GetByEpicID(epicID)
	if err != nil {
		return fmt.Errorf("failed to get issues for epic %s: %v", epicID, err)
	}

	records, err := s.CalculateBurndownForEpic(epicID, issues, startYear, startWeek, endYear, endWeek)
	if err != nil {
		return fmt.Errorf("failed to calculate burndown for epic %s: %v", epicID, err)
	}

	err = s.SaveBurndownData(epicID, records)
	if err != nil {
		return fmt.Errorf("failed to save burndown data for epic %s: %v", epicID, err)
	}

	log.Printf("Successfully calculated and saved %d burndown records for epic %s", len(records), epicID)
	return nil
}
