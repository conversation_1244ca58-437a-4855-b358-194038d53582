package usecase

import (
	"excelize-test/models"
	"excelize-test/storage/repository/mysql"
	"fmt"
	"log"
	"strings"
	"time"
)

type BurndownUsecase struct {
	burndownRepo *mysql.BurndownRepository
	issueRepo    *mysql.IssueRepository
}

func NewBurndownUsecase() *BurndownUsecase {
	return &BurndownUsecase{
		burndownRepo: mysql.NewBurndownRepository(),
		issueRepo:    mysql.NewIssueRepository(),
	}
}

func (s *BurndownUsecase) CalculateBurndownForEpic(epicID string, issues []mysql.Issue, startYear, startWeek, endYear, endWeek int) (models.BurndownRecords, error) {
	epicIssues := s.filterIssuesForEpic(issues, epicID)
	totalEstimate := s.calculateTotalEstimate(epicIssues)
	log.Printf("issues found for epic %s %d ", epicID, len(epicIssues))
	log.Printf("total estimate for epic %s: %f", epicID, totalEstimate)

	var burndownRecords models.BurndownRecords

	for year := startYear; year <= endYear; year++ {
		weekStart := 1
		weekEnd := 52

		if year == startYear {
			weekStart = startWeek
		}
		if year == endYear {
			weekEnd = endWeek
		}

		for week := weekStart; week <= weekEnd; week++ {
			actual := s.calculateActualRemainingForWeek(epicIssues, year, week)
			workCompleted := s.calculateWorkCompletedByWeek(epicID, year, week)
			ideal := totalEstimate - workCompleted
			if ideal < 0 {
				ideal = 0
			}

			record := models.BurndownRecord{
				PK:     fmt.Sprintf("%s_%d_%d", epicID, year, week),
				EpicID: epicID,
				Year:   year,
				Week:   week,
				Actual: actual,
				Ideal:  ideal,
			}

			burndownRecords = append(burndownRecords, record)
		}
	}

	return burndownRecords, nil
}

func (s *BurndownUsecase) filterIssuesForEpic(issues []mysql.Issue, epicID string) []mysql.Issue {
	var epicIssues []mysql.Issue

	for _, issue := range issues {
		if issue.Key == epicID || issue.ParentKey == epicID {
			epicIssues = append(epicIssues, issue)
		}
	}

	return epicIssues
}

func (s *BurndownUsecase) calculateTotalEstimate(issues []mysql.Issue) float64 {
	total := 0.0
	for _, issue := range issues {
		total += float64(issue.TimeOriginalEstimate) / 3600.0
	}
	return total
}

func (s *BurndownUsecase) calculateActualRemaining(issues []mysql.Issue) float64 {
	remaining := 0.0

	for _, issue := range issues {
		if !s.isTaskCompleted(issue.StatusName) {
			remaining += float64(issue.TimeOriginalEstimate) / 3600.0
		}
	}

	return remaining
}

// Step-by-step calculation for each week
func (s *BurndownUsecase) calculateActualRemainingForWeek(issues []mysql.Issue, targetYear, targetWeek int) float64 {
	remaining := 0.0
	scRepo := mysql.NewStatusChangeRepository()

	// Step 2: Take week start and end date
	weekStartTimestamp := s.getWeekStartTimestamp(targetYear, targetWeek)
	weekEndTimestamp := s.getWeekEndTimestamp(targetYear, targetWeek)

	log.Printf("DEBUG: Week %d-%d - Start: %d, End: %d",
		targetYear, targetWeek, weekStartTimestamp, weekEndTimestamp)

	// Step 3: Iterate through issues
	for _, issue := range issues {
		log.Printf("DEBUG: Processing issue %s", issue.Key)

		// Step 4: Find status changes using key
		statusChanges, err := scRepo.GetByKey(issue.Key)
		if err != nil {
			log.Printf("ERROR: Failed to get status changes for %s: %v", issue.Key, err)
			continue
		}

		// Step 5: Check if they are in this week
		var statusChangeInWeek *mysql.StatusChange = nil
		for _, change := range statusChanges {
			if change.Date >= weekStartTimestamp && change.Date <= weekEndTimestamp {
				// Step 6: Check if they are to status done
				if s.isTaskCompleted(change.ToStatusName) {
					statusChangeInWeek = &change
					log.Printf("DEBUG: Issue %s completed in week %d-%d (status: %s)",
						issue.Key, targetYear, targetWeek, change.ToStatusName)
					break
				}
			}
		}

		// Step 7: If yes, skip
		if statusChangeInWeek != nil {
			log.Printf("DEBUG: Issue %s - COMPLETED in this week - Skipping", issue.Key)
			continue
		}

		// Step 8: If no, add their origin estimate to the current week
		hours := float64(issue.TimeOriginalEstimate) / 3600.0
		remaining += hours
		log.Printf("DEBUG: Issue %s - NOT completed in this week - Adding %.2f hours", issue.Key, hours)
	}

	log.Printf("DEBUG: Week %d-%d total remaining: %.2f hours", targetYear, targetWeek, remaining)
	return remaining
}

func (s *BurndownUsecase) isTaskCompleted(statusName string) bool {
	completedStatuses := []string{"Done Develop*", "Done Develop"}
	statusLower := strings.ToLower(statusName)

	for _, status := range completedStatuses {
		if strings.ToLower(status) == statusLower {
			return true
		}
	}

	return false
}

func (s *BurndownUsecase) calculateWorkCompletedByWeek(epicID string, targetYear, targetWeek int) float64 {
	var timesheetRecords []mysql.TimesheetRecord

	query := mysql.Db.Where("epic = ?", epicID)
	query = query.Where(
		"(CAST(year AS UNSIGNED) < ?) OR (CAST(year AS UNSIGNED) = ? AND CAST(week AS UNSIGNED) <= ?)",
		targetYear, targetYear, targetWeek,
	)

	err := query.Find(&timesheetRecords).Error
	if err != nil {
		log.Printf("Error querying timesheet records for epic %s: %v", epicID, err)
		return 0.0
	}

	totalWork := 0.0
	const HOURS = 40
	for _, record := range timesheetRecords {
		totalWork += record.EstimateEffort * HOURS
	}

	return totalWork
}

func (s *BurndownUsecase) SaveBurndownData(epicID string, records models.BurndownRecords) error {
	return s.burndownRepo.Update(epicID, records)
}

func (s *BurndownUsecase) GetBurndownData(epicID string) (models.BurndownRecords, error) {
	return s.burndownRepo.GetByEpic(epicID)
}

func (s *BurndownUsecase) CalculateAndSaveBurndownForEpic(epicID string, startYear, startWeek, endYear, endWeek int) error {

	issues, err := s.issueRepo.GetByEpicID(epicID)
	if err != nil {
		return fmt.Errorf("failed to get issues for epic %s: %v", epicID, err)
	}

	records, err := s.CalculateBurndownForEpic(epicID, issues, startYear, startWeek, endYear, endWeek)
	if err != nil {
		return fmt.Errorf("failed to calculate burndown for epic %s: %v", epicID, err)
	}

	err = s.SaveBurndownData(epicID, records)
	if err != nil {
		return fmt.Errorf("failed to save burndown data for epic %s: %v", epicID, err)
	}

	log.Printf("Successfully calculated and saved %d burndown records for epic %s", len(records), epicID)
	return nil
}

// getWeekStartTimestamp конвертирует год и неделю в timestamp начала недели
func (s *BurndownUsecase) getWeekStartTimestamp(year, week int) uint64 {
	// Находим первый понедельник года
	jan1 := time.Date(year, 1, 1, 0, 0, 0, 0, time.UTC)

	// Находим первый понедельник года
	daysToMonday := (8 - int(jan1.Weekday())) % 7
	if jan1.Weekday() == time.Sunday {
		daysToMonday = 1
	}
	firstMonday := jan1.AddDate(0, 0, daysToMonday)

	// Добавляем недели для получения начала целевой недели
	targetWeekStart := firstMonday.AddDate(0, 0, (week-1)*7)

	return uint64(targetWeekStart.UnixNano())
}

// getWeekEndTimestamp конвертирует год и неделю в timestamp конца недели
func (s *BurndownUsecase) getWeekEndTimestamp(year, week int) uint64 {
	// Находим первый понедельник года
	jan1 := time.Date(year, 1, 1, 0, 0, 0, 0, time.UTC)

	// Находим первый понедельник года
	daysToMonday := (8 - int(jan1.Weekday())) % 7
	if jan1.Weekday() == time.Sunday {
		daysToMonday = 1
	}
	firstMonday := jan1.AddDate(0, 0, daysToMonday)

	// Добавляем недели и переходим к воскресенью (концу недели)
	targetWeekStart := firstMonday.AddDate(0, 0, (week-1)*7)
	weekEnd := targetWeekStart.AddDate(0, 0, 6).Add(23*time.Hour + 59*time.Minute + 59*time.Second)

	return uint64(weekEnd.UnixNano())
}
