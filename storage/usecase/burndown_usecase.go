package usecase

import (
	"excelize-test/models"
	"excelize-test/storage/repository/mysql"
	"fmt"
	"log"
	"strings"
)

type BurndownUsecase struct {
	burndownRepo *mysql.BurndownRepository
	issueRepo    *mysql.IssueRepository
}

func NewBurndownUsecase() *BurndownUsecase {
	return &BurndownUsecase{
		burndownRepo: mysql.NewBurndownRepository(),
		issueRepo:    mysql.NewIssueRepository(),
	}
}

func (s *BurndownUsecase) CalculateBurndownForEpic(epicID string, issues []mysql.Issue, startYear, startWeek, endYear, endWeek int) (models.BurndownRecords, error) {
	epicIssues := s.filterIssuesForEpic(issues, epicID)
	totalEstimate := s.calculateTotalEstimate(epicIssues)

	var burndownRecords models.BurndownRecords

	for year := startYear; year <= endYear; year++ {
		weekStart := 1
		weekEnd := 52

		if year == startYear {
			weekStart = startWeek
		}
		if year == endYear {
			weekEnd = endWeek
		}

		for week := weekStart; week <= weekEnd; week++ {
			actual := s.calculateActualRemaining(epicIssues)
			workCompleted := s.calculateWorkCompletedByWeek(epicID, year, week)
			ideal := totalEstimate - workCompleted
			if ideal < 0 {
				ideal = 0
			}

			record := models.BurndownRecord{
				PK:     fmt.Sprintf("%s_%d_%d", epicID, year, week),
				EpicID: epicID,
				Year:   year,
				Week:   week,
				Actual: actual,
				Ideal:  ideal,
			}

			burndownRecords = append(burndownRecords, record)
		}
	}

	return burndownRecords, nil
}

func (s *BurndownUsecase) filterIssuesForEpic(issues []mysql.Issue, epicID string) []mysql.Issue {
	var epicIssues []mysql.Issue

	for _, issue := range issues {
		if issue.Key == epicID || issue.ParentKey == epicID {
			epicIssues = append(epicIssues, issue)
		}
	}

	return epicIssues
}

func (s *BurndownUsecase) calculateTotalEstimate(issues []mysql.Issue) float64 {
	total := 0.0
	for _, issue := range issues {
		total += float64(issue.TimeOriginalEstimate) / 3600.0
	}
	return total
}

func (s *BurndownUsecase) calculateActualRemaining(issues []mysql.Issue) float64 {
	remaining := 0.0

	scRepo := mysql.NewStatusChangeRepository()

	for _, issue := range issues {
		changes, err := scRepo.GetByKey(issue.Key)
		if err != nil {
			log.Printf("Error getting status changes for issue %s: %v", issue.Key, err)
			continue
		}
		for _, change := range changes {
			fmt.Printf("Change: %+v\n", change)
			if s.isTaskCompleted(change.ToStatusName) {
				remaining -= float64(issue.TimeOriginalEstimate) / 3600.0
				break
			} else {
				remaining += float64(issue.TimeOriginalEstimate) / 3600.0
			}
		}
		if !s.isTaskCompleted(issue.StatusName) {
			remaining += float64(issue.TimeOriginalEstimate) / 3600.0
		}
	}

	return remaining
}

func (s *BurndownUsecase) isTaskCompleted(statusName string) bool {
	completedStatuses := []string{"Done Develop*"}
	statusLower := strings.ToLower(statusName)

	for _, status := range completedStatuses {
		if strings.ToLower(status) == statusLower {
			return true
		}
	}

	return false
}

func (s *BurndownUsecase) calculateWorkCompletedByWeek(epicID string, targetYear, targetWeek int) float64 {
	var timesheetRecords []mysql.TimesheetRecord

	query := mysql.Db.Where("epic = ?", epicID)
	query = query.Where(
		"(CAST(year AS UNSIGNED) < ?) OR (CAST(year AS UNSIGNED) = ? AND CAST(week AS UNSIGNED) <= ?)",
		targetYear, targetYear, targetWeek,
	)

	err := query.Find(&timesheetRecords).Error
	if err != nil {
		log.Printf("Error querying timesheet records for epic %s: %v", epicID, err)
		return 0.0
	}

	totalWork := 0.0
	const HOURS = 40
	for _, record := range timesheetRecords {
		totalWork += record.EstimateEffort * HOURS
	}

	return totalWork
}

func (s *BurndownUsecase) SaveBurndownData(epicID string, records models.BurndownRecords) error {
	return s.burndownRepo.Update(epicID, records)
}

func (s *BurndownUsecase) GetBurndownData(epicID string) (models.BurndownRecords, error) {
	return s.burndownRepo.GetByEpic(epicID)
}

func (s *BurndownUsecase) CalculateAndSaveBurndownForEpic(epicID string, startYear, startWeek, endYear, endWeek int) error {

	issues, err := s.issueRepo.GetByEpicID(epicID)
	if err != nil {
		return fmt.Errorf("failed to get issues for epic %s: %v", epicID, err)
	}

	records, err := s.CalculateBurndownForEpic(epicID, issues, startYear, startWeek, endYear, endWeek)
	if err != nil {
		return fmt.Errorf("failed to calculate burndown for epic %s: %v", epicID, err)
	}

	err = s.SaveBurndownData(epicID, records)
	if err != nil {
		return fmt.Errorf("failed to save burndown data for epic %s: %v", epicID, err)
	}

	log.Printf("Successfully calculated and saved %d burndown records for epic %s", len(records), epicID)
	return nil
}
