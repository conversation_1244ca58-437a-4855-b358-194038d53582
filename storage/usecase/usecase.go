package usecase

import (
	"excelize-test/models"
	"excelize-test/storage/repository"
	"log"
)

type TimesheetUsecase struct {
	repository repository.TimesheetRepository
}

func NewTimesheetUsecase(repository repository.TimesheetRepository) *TimesheetUsecase {
	return &TimesheetUsecase{repository: repository}
}

func (t *TimesheetUsecase) Update(records models.TimesheetRecords) error {
	log.Println("Updating timesheet records")
	return t.repository.Update(records)
}
