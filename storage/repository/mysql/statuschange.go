package mysql

type StatusChange struct {
	Key            string  `gorm:"column:key;size:255;primaryKey"`
	ToStatusName   string  `gorm:"column:toStatusName;size:255"`
	ToStatusId     int     `gorm:"column:toStatusId"`
	FromStatusName *string `gorm:"column:fromStatusName;size:255"`
	FromStatusId   *int    `gorm:"column:fromStatusId"`
	Date           uint64  `gorm:"column:date"`
}

func (o StatusChange) TableName() string {

	return "test_statuschanges_table"
}
