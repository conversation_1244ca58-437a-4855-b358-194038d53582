package mysql

import (
	"excelize-test/models"
	"gorm.io/gorm"
	"log"
)

type BurndownRepository struct {
}

func NewBurndownRepository() *BurndownRepository {
	return &BurndownRepository{}
}

func (b *BurndownRepository) CreateMany(records models.BurndownRecords) error {
	err := Db.Transaction(func(tx *gorm.DB) error {
		var burndownRecords []BurndownRecord
		for _, record := range records {
			dbRecord := BurndownFromModel(record)
			if dbRecord.PK == "" {
				dbRecord.PK = dbRecord.GeneratePK()
			}
			burndownRecords = append(burndownRecords, dbRecord)
		}
		err := tx.Create(&burndownRecords).Error
		if err != nil {
			log.Println("Error creating burndown records:", err)
			return err
		}
		return nil
	})
	return err
}

func (b *BurndownRepository) Update(epicID string, records models.BurndownRecords) error {
	err := Db.Transaction(func(tx *gorm.DB) error {
		err := tx.Where("epic_id = ?", epicID).Delete(&BurndownRecord{}).Error
		if err != nil {
			log.Println("Error deleting existing burndown records:", err)
			return err
		}

		if len(records) > 0 {
			var burndownRecords []BurndownRecord
			for _, record := range records {
				dbRecord := BurndownFromModel(record)
				if dbRecord.PK == "" {
					dbRecord.PK = dbRecord.GeneratePK()
				}
				burndownRecords = append(burndownRecords, dbRecord)
			}
			err = tx.Create(&burndownRecords).Error
			if err != nil {
				log.Println("Error creating new burndown records:", err)
				return err
			}
		}
		return nil
	})
	return err
}

func (b *BurndownRepository) GetByEpic(epicID string) (models.BurndownRecords, error) {
	var records []BurndownRecord
	err := Db.Where("epic_id = ?", epicID).Order("year ASC, week ASC").Find(&records).Error
	if err != nil {
		log.Println("Error fetching burndown records:", err)
		return nil, err
	}

	var result models.BurndownRecords
	for _, record := range records {
		result = append(result, record.ToModel())
	}
	return result, nil
}
