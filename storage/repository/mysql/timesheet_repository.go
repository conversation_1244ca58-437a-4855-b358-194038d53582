package mysql

import (
	"excelize-test/models"
	"gorm.io/gorm"
	"log"
)

var (
	Db *gorm.DB
)

type TimesheetRepository struct {
}

func NewTimesheetRepository() *TimesheetRepository {
	return &TimesheetRepository{}
}

func (t *TimesheetRepository) CreateMany(records models.TimesheetRecords) error {
	err := Db.Transaction(func(tx *gorm.DB) error {
		var sms []TimesheetRecord
		for _, c := range records {
			sms = append(sms, IssueFromModel(c))
		}
		err := tx.Create(&sms).Error
		if err != nil {
			log.Println(err)
			return err
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func (t *TimesheetRepository) Update(commands models.TimesheetRecords) error {
	err := Db.Transaction(func(tx *gorm.DB) error {
		err := t.Delete()
		if err != nil {
			return err
		}
		err = t.<PERSON><PERSON>(commands)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}
func (t *TimesheetRepository) Delete() error {
	err := Db.Transaction(func(tx *gorm.DB) error {
		err := tx.Session(&gorm.Session{AllowGlobalUpdate: true}).Delete(&TimesheetRecord{}).Error
		if err != nil {
			log.Println(err)
			return err
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}
