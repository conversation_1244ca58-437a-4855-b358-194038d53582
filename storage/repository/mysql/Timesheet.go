package mysql

import (
	"excelize-test/models"
)

type TimesheetRecord struct {
	PK             string  `gorm:"primaryKey" json:"pk"`
	Initials       string  `gorm:"column:initials;size:255;not null" json:"initials"`
	Epic           string  `gorm:"column:epic;size:255;not null" json:"epic"` // [XXXX-XXXX]
	Year           string  `gorm:"column:year;size:255;not null" json:"year"`
	Week           string  `gorm:"column:week;size:255;not null" json:"week"`
	Startdate      uint64  `gorm:"column:startdate"`
	Enddate        uint64  `gorm:"column:enddate;" json:"enddate"`
	EstimateEffort float64 `gorm:"column:estimate_effort;type:decimal(5,2);not null" json:"estimate_effort"` // 0.6
}

func (TimesheetRecord) TableName() string {
	return "timesheet_records"
}

func IssueFromModel(model models.TimesheetRecord) TimesheetRecord {
	// Helper function to safely dereference string pointers

	return TimesheetRecord{
		PK:             model.PK,
		Initials:       model.Initials,
		Epic:           model.Epic,
		Year:           model.Year,
		Week:           model.Week,
		Startdate:      uint64(model.Startdate.UnixNano()),
		Enddate:        uint64(model.Enddate.UnixNano()),
		EstimateEffort: model.EstimateEffort,
	}
}
