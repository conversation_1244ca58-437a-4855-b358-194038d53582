package mysql

import (
	"excelize-test/models"
	"log"
	"strings"
	"time"
)

type TimesheetRecord struct {
	PK             string  `gorm:"primaryKey" json:"pk"`
	Initials       string  `gorm:"column:initials;size:255;not null" json:"initials"`
	Epic           string  `gorm:"column:epic;size:255;not null" json:"epic"` // [XXXX-XXXX]
	Year           string  `gorm:"column:year;size:255;not null" json:"year"`
	Week           string  `gorm:"column:week;size:255;not null" json:"week"`
	Startdate      uint    `gorm:"column:startdate"`
	Enddate        uint    `gorm:"column:enddate;" json:"enddate"`
	EstimateEffort float64 `gorm:"column:estimate_effort;type:decimal(5,2);not null" json:"estimate_effort"` // 0.6
}

func (TimesheetRecord) TableName() string {
	return "timesheet_records"
}

func IssueFromModel(model models.TimesheetRecord) TimesheetRecord {
	// Helper function to safely dereference string pointers

	safeDate := func(t string) uint {
		if t == "" {
			return 0
		}

		t = strings.TrimSpace(t)

		// Parse time string in format "2025-12-01 00:00"
		parsedTime, err := time.Parse("2006-01-02 15:04", t)
		if err != nil {
			log.Println("Error parsing time:", err)
			return 0
		}

		return uint(parsedTime.Unix())
	}

	return TimesheetRecord{
		PK:             model.PK,
		Initials:       model.Initials,
		Epic:           model.Epic,
		Year:           model.Year,
		Week:           model.Week,
		Startdate:      safeDate(model.Startdate),
		Enddate:        safeDate(model.Enddate),
		EstimateEffort: model.EstimateEffort,
	}
}
