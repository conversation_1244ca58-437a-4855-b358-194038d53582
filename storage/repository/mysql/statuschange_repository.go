package mysql

import "log"

type StatusChangeRepository struct {
}

func NewStatusChangeRepository() *StatusChangeRepository {
	return &StatusChangeRepository{}
}

func (i *StatusChangeRepository) GetByKey(key string) ([]StatusChange, error) {
	var statusChanges []StatusChange
	log.Printf("DEBUG: Querying status changes for key: %s", key)
	err := Db.Where("key = ?", key).Order("date ASC").Find(&statusChanges).Error
	if err != nil {
		log.Printf("ERROR: Failed to query status changes: %v", err)
		return nil, err
	}
	log.Printf("DEBUG: Found %d status changes for key %s", len(statusChanges), key)
	return statusChanges, nil
}

func (i *StatusChangeRepository) GetByKeyBeforeDate(key string, beforeDate uint64) ([]StatusChange, error) {
	var statusChanges []StatusChange
	log.Printf("DEBUG: Querying status changes for key: %s before date: %d", key, beforeDate)
	err := Db.Where("key = ? AND date <= ?", key, beforeDate).Order("date ASC").Find(&statusChanges).Error
	if err != nil {
		log.Printf("ERROR: Failed to query status changes: %v", err)
		return nil, err
	}
	log.Printf("DEBUG: Found %d status changes for key %s before date %d", len(statusChanges), key, beforeDate)
	return statusChanges, nil
}

// GetTableInfo returns information about the statuschanges_table structure
func (i *StatusChangeRepository) GetTableInfo() error {
	var result []map[string]interface{}
	err := Db.Raw("DESCRIBE statuschanges_table").Scan(&result).Error
	if err != nil {
		log.Printf("ERROR: Failed to describe table: %v", err)
		return err
	}

	log.Printf("DEBUG: statuschanges_table structure:")
	for _, row := range result {
		log.Printf("  %+v", row)
	}
	return nil
}

// GetSampleData returns a few sample records to check data format
func (i *StatusChangeRepository) GetSampleData() error {
	var statusChanges []StatusChange
	err := Db.Limit(5).Find(&statusChanges).Error
	if err != nil {
		log.Printf("ERROR: Failed to get sample data: %v", err)
		return err
	}

	log.Printf("DEBUG: Sample status changes (first 5 records):")
	for _, change := range statusChanges {
		log.Printf("  Key: %s, ToStatus: %s, Date: %d", change.Key, change.ToStatusName, change.Date)
	}
	return nil
}
