package mysql

type StatusChangeRepository struct {
}

func NewStatusChangeRepository() *StatusChangeRepository {
	return &StatusChangeRepository{}
}

func (i *StatusChangeRepository) GetByKey(key string) ([]StatusChange, error) {
	var statusChanges []StatusChange
	err := Db.Where("key = ?", key).Order("date ASC").Find(&statusChanges).Error
	if err != nil {
		return nil, err
	}
	return statusChanges, nil
}

func (i *StatusChangeRepository) GetByKeyBeforeDate(key string, beforeDate uint64) ([]StatusChange, error) {
	var statusChanges []StatusChange
	err := Db.Where("key = ? AND date <= ?", key, beforeDate).Order("date ASC").Find(&statusChanges).Error
	if err != nil {
		return nil, err
	}
	return statusChanges, nil
}
