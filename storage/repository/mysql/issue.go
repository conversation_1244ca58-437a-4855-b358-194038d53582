package mysql

type Issue struct {
	Key                  string `gorm:"column:key;size:255;primaryKey"`
	Summary              string `gorm:"column:summary;type:text"`
	AssigneeName         string `gorm:"column:assigneeName;size:255"`
	SecondAssigneeName   string `gorm:"column:secondAssigneeName;size:255"`
	ReporterName         string `gorm:"column:reporterName;size:255"`
	StatusName           string `gorm:"column:statusName;size:255"`
	StatusId             int    `gorm:"column:statusId"`
	IssueTypeName        string `gorm:"column:issueTypeName;size:255"`
	PriorityId           int    `gorm:"column:priorityId"`
	PriorityName         string `gorm:"column:priorityName;size:255"`
	Created              uint64 `gorm:"column:created"`
	Updated              uint64 `gorm:"column:updated"`
	Labels               string `gorm:"column:labels;size:255"`
	Resolution           string `gorm:"column:resolution;size:255"`
	ResolutionDate       uint64 `gorm:"column:resolutionDate"`
	TimeOriginalEstimate int    `gorm:"column:timeOriginalEstimate"`
	TimeRemaining        int    `gorm:"column:timeRemaining"`
	Components           string `gorm:"column:components;size:255"`
	ParentKey            string `gorm:"column:parentKey;size:255"`
	ParentName           string `gorm:"column:parentName;size:255"`
	SprintName           string `gorm:"column:sprintName;size:255"`
	Client               string `gorm:"column:client;size:255"`
	Rank                 string `gorm:"column:rank;size:255"`
	StoryPoints          int    `gorm:"column:storyPoints"`
	ProjectKey           string `gorm:"column:projectKey;size:255"`
	ProjectName          string `gorm:"column:projectName;size:255"`
	DueDate              uint64 `gorm:"column:dueDate"`
}

func (Issue) TableName() string {
	return "issues_table"
}
