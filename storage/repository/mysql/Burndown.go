package mysql

import (
	"excelize-test/models"
	"fmt"
)

type BurndownRecord struct {
	PK     string  `gorm:"primaryKey" json:"pk"`
	EpicID string  `gorm:"column:epic_id;size:255;not null;index" json:"epic_id"`
	Year   int     `gorm:"column:year;not null;index" json:"year"`
	Week   int     `gorm:"column:week;not null;index" json:"week"`
	Actual float64 `gorm:"column:actual;type:decimal(10,2);not null" json:"actual"`
	Ideal  float64 `gorm:"column:ideal;type:decimal(10,2);not null" json:"ideal"`
}

func (BurndownRecord) TableName() string {
	return "burndown_records"
}

func BurndownFromModel(model models.BurndownRecord) BurndownRecord {
	return BurndownRecord{
		PK:     model.PK,
		EpicID: model.EpicID,
		Year:   model.Year,
		Week:   model.Week,
		Actual: model.Actual,
		Ideal:  model.Ideal,
	}
}

func (b BurndownRecord) ToModel() models.BurndownRecord {
	return models.BurndownRecord{
		PK:     b.PK,
		EpicID: b.EpicID,
		Year:   b.Year,
		Week:   b.Week,
		Actual: b.Actual,
		Ideal:  b.Ideal,
	}
}

func (b BurndownRecord) GeneratePK() string {
	return fmt.Sprintf("%s_%d_%d", b.EpicID, b.Year, b.Week)
}
