package mysql

type IssueRepository struct {
}

func NewIssueRepository() *IssueRepository {
	return &IssueRepository{}
}

func (i *IssueRepository) GetByEpicID(epicID string) ([]Issue, error) {
	var issues []Issue
	err := Db.Where("parentKey = ?", epicID).Find(&issues).Error
	if err != nil {
		return nil, err
	}
	return issues, nil
}

func (i *IssueRepository) GetAll() ([]Issue, error) {
	var issues []Issue
	err := Db.Find(&issues).Error
	if err != nil {
		return nil, err
	}
	return issues, nil
}
