package main

import (
	"bytes"
	"excelize-test/models"
	"fmt"
	"github.com/xuri/excelize/v2"
	"io"
	"mime/multipart"
)

func processExcelFile(file multipart.File) ([]models.TimesheetRecord, error) {
	// Read file content into memory
	data, err := io.ReadAll(file)
	if err != nil {
		return nil, fmt.Errorf("failed to read file: %w", err)
	}

	// Open Excel file from bytes using bytes.Reader
	f, err := excelize.OpenReader(bytes.NewReader(data))
	if err != nil {
		return nil, fmt.Errorf("failed to open Excel file: %w", err)
	}
	defer func() {
		if closeErr := f.Close(); closeErr != nil {
			fmt.Printf("Error closing Excel file: %v\n", closeErr)
		}
	}()

	// Process the Excel file using existing logic
	return processDataOfExcelFile(f)
}

func processDataOfExcelFile(f *excelize.File) ([]models.TimesheetRecord, error) {
	firstRow, lastRow, firstCol, lastCol, err := findDataBoundaries(f)
	if err != nil {
		return nil, err
	}
	if firstRow == 0 || lastRow == 0 || firstCol == "" || lastCol == "" {
		return nil, fmt.Errorf("no valid data found in Excel file")
	}

	fmt.Printf("Data found: rows %d-%d, columns %s-%s\n", firstRow, lastRow, firstCol, lastCol)

	weekHeaders := getAllWeekHeaders(f, firstCol, lastCol)
	if len(weekHeaders) == 0 {
		return nil, fmt.Errorf("no week headers found in Excel file")
	}

	fmt.Printf("Found %d weeks: %v\n", len(weekHeaders), weekHeaders)

	var records []models.TimesheetRecord

	for row := firstRow; row <= lastRow; row++ {
		name, epic, err := getNameAndEpic(f, row)
		if err != nil {
			return nil, err
		}

		if name == "" || epic == "" {
			continue
		}

		fmt.Printf("Processing %s - %s\n", name, epic)

		// Loop through each week column
		firstColNum, _ := excelize.ColumnNameToNumber(firstCol)
		for i, weekHeader := range weekHeaders {
			colNum := firstColNum + i
			colName, _ := excelize.ColumnNumberToName(colNum)

			// Get effort value from cell
			cellRef := fmt.Sprintf("%s%d", colName, row)
			cellValue, err := f.GetCellValue(WorksheetName, cellRef)

			if err != nil || cellValue == "" {
				continue // Skip empty cells
			}

			// Parse effort
			effort, err := getEstimatedEffort(cellValue)
			if err != nil {
				return nil, fmt.Errorf("failed to parse effort: %w", err)
			}

			// Parse dates
			startDate, endDate, year, week, err := getDates(weekHeader)
			if year == "" || week == "" || err != nil {
				return nil, err
			}

			// Create record
			record := models.TimesheetRecord{
				PK:             fmt.Sprintf("%s_%s_%s_W%s", name, epic, year, week),
				Initials:       name,
				Epic:           epic,
				Year:           year,
				Week:           week,
				Startdate:      startDate.Format("2006-01-02 15:04"),
				Enddate:        endDate.Format("2006-01-02 15:04"),
				EstimateEffort: effort,
			}

			records = append(records, record)
			fmt.Printf("  Added: Week %s = %.0f%%\n", week, effort*100)
		}
	}

	fmt.Printf("Total records: %d\n", len(records))

	return records, nil
}

func findDataBoundaries(f *excelize.File) (int, int, string, string, error) {
	firstRow := 0
	lastRow := 0
	firstCol := ""
	lastCol := ""
	var started bool = false

	// 1. Find rows with [initials] pattern in column B using pre-compiled regex
	for row := 1; row <= MaxSearchRows; row++ {
		cellValue, _ := f.GetCellValue(WorksheetName, fmt.Sprintf("%s%d", NameColumn, row))

		if cellValue == "Name" {
			started = true
			continue
		}
		if BracketRegex.MatchString(cellValue) {
			if firstRow == 0 {
				firstRow = row
				fmt.Printf("First employee row: %d (%s)\n", row, cellValue)
			}
			lastRow = row
		} else {
			if started {
				if cellValue == "" {
					break
				} else {
					return 0, 0, "", "", fmt.Errorf("no initials found in row %d", row)
				}
			}
		}
	}
	started = false

	// 2. Find columns with XX-XX pattern using pre-compiled regex
	for col := 1; col <= MaxSearchColumns; col++ {
		colName, _ := excelize.ColumnNumberToName(col)
		cellValue, _ := f.GetCellValue(WorksheetName, fmt.Sprintf("%s%d", colName, HeaderRow))

		if cellValue != "" {
			started = true
		}

		if WeekRegex.MatchString(cellValue) {
			if firstCol == "" {
				firstCol = colName
				fmt.Printf("First week column: %s (%s)\n", colName, cellValue)
			}
			lastCol = colName
		} else {
			if started {
				if cellValue == "" {
					break
				} else if !WeekRegex.MatchString(cellValue) {
					return 0, 0, "", "", fmt.Errorf("wrong format in column %d", col)
				}
			}
		}
	}

	fmt.Printf("Last employee row: %d\n", lastRow)
	fmt.Printf("Last week column: %s\n", lastCol)

	return firstRow, lastRow, firstCol, lastCol, nil
}

func getNameAndEpic(f *excelize.File, row int) (string, string, error) {
	name, err := f.GetCellValue(WorksheetName, fmt.Sprintf("%s%d", NameColumn, row))
	if err != nil {
		fmt.Println(err)
		return "", "", fmt.Errorf("failed to get name from cell: %w", err)
	}

	epic, err := f.GetCellValue(WorksheetName, fmt.Sprintf("%s%d", EpicColumn, row))
	if err != nil {
		fmt.Println(err)
		return "", "", nil
	}

	name, err = extract(name, true)
	if err != nil {
		return "", "", fmt.Errorf("failed to get name from cell: %w", err)
	}
	epic, _ = extract(epic, false)

	return name, epic, nil
}

func getAllWeekHeaders(f *excelize.File, firstCol, lastCol string) []string {
	var weekHeaders []string

	firstColNum, _ := excelize.ColumnNameToNumber(firstCol)
	lastColNum, _ := excelize.ColumnNameToNumber(lastCol)

	for col := firstColNum; col <= lastColNum; col++ {
		colName, _ := excelize.ColumnNumberToName(col)
		cellValue, _ := f.GetCellValue(WorksheetName, fmt.Sprintf("%s%d", colName, HeaderRow))

		if cellValue != "" {
			weekHeaders = append(weekHeaders, cellValue)
		}
	}

	return weekHeaders
}
